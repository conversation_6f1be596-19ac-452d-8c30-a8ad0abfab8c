"""
推理脚本 - 测试微调后的模型
"""

import torch
from transformers import AutoTokenizer, AutoModelForCausalLM
from peft import PeftModel
from config import Config

class ModelInference:
    def __init__(self, model_path=None):
        self.config = Config()
        self.model_path = model_path or self.config.OUTPUT_DIR
        self.model = None
        self.tokenizer = None
        self.load_model()
    
    def load_model(self):
        """加载微调后的模型"""
        print(f"加载模型从: {self.model_path}")
        
        # 加载tokenizer
        self.tokenizer = AutoTokenizer.from_pretrained(
            self.config.MODEL_NAME,
            trust_remote_code=True
        )
        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token
        
        # 加载基础模型
        base_model = AutoModelForCausalLM.from_pretrained(
            self.config.MODEL_NAME,
            torch_dtype=torch.float16,
            device_map="auto",
            trust_remote_code=True
        )
        
        # 加载LoRA权重
        self.model = PeftModel.from_pretrained(base_model, self.model_path)
        self.model.eval()
        
        print("模型加载完成！")
    
    def generate_response(self, instruction, input_text="", max_length=512, temperature=0.7):
        """生成回答"""
        # 格式化输入
        if input_text:
            prompt = f"User: {instruction}\n\n{input_text}\n\nAssistant:"
        else:
            prompt = f"User: {instruction}\n\nAssistant:"
        
        # tokenize
        inputs = self.tokenizer(
            prompt,
            return_tensors="pt",
            truncation=True,
            max_length=1024
        ).to(self.model.device)
        
        # 生成
        with torch.no_grad():
            outputs = self.model.generate(
                **inputs,
                max_new_tokens=max_length,
                temperature=temperature,
                do_sample=True,
                top_p=0.9,
                repetition_penalty=1.1,
                pad_token_id=self.tokenizer.eos_token_id,
                eos_token_id=self.tokenizer.eos_token_id,
            )
        
        # 解码
        response = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
        
        # 提取Assistant的回答
        if "Assistant:" in response:
            response = response.split("Assistant:")[-1].strip()
        
        return response
    
    def interactive_chat(self):
        """交互式对话"""
        print("\n=== 交互式测试 ===")
        print("输入 'quit' 退出")
        print("-" * 30)
        
        while True:
            instruction = input("\n请输入指令: ").strip()
            if instruction.lower() == 'quit':
                break
            
            input_text = input("请输入上下文（可选，直接回车跳过）: ").strip()
            
            print("\n生成中...")
            response = self.generate_response(instruction, input_text)
            print(f"\n回答: {response}")
            print("-" * 50)

def test_water_engineering_examples():
    """测试水利工程相关问题"""
    inference = ModelInference()
    
    # 测试用例
    test_cases = [
        {
            "instruction": "你是一个水利工程专家，请根据提供的上下文回答问题。",
            "input": "上下文：时间: 2020-01-25 06, 闸前水位(m): 144.796, 闸后水位(m): 143.573, 开度(mm): 1380/1330, 瞬时流量(m³/s): 147.274\n\n问题：闸前水位是多少？",
        },
        {
            "instruction": "你是一个水利工程专家，请根据提供的上下文回答问题。",
            "input": "上下文：闸前水位从144.85米变化到144.90米，瞬时流量从320 m³/s变化到325 m³/s\n\n问题：水位和流量的变化趋势如何？",
        }
    ]
    
    print("\n=== 测试水利工程问题 ===")
    for i, case in enumerate(test_cases, 1):
        print(f"\n测试 {i}:")
        print(f"指令: {case['instruction']}")
        print(f"输入: {case['input']}")
        
        response = inference.generate_response(case['instruction'], case['input'])
        print(f"回答: {response}")
        print("-" * 50)

if __name__ == "__main__":
    # 可以选择运行测试或交互式对话
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "test":
        test_water_engineering_examples()
    else:
        inference = ModelInference()
        inference.interactive_chat()
