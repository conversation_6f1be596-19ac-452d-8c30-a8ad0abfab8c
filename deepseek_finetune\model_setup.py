"""
模型设置模块 - 加载模型并配置LoRA
"""

import torch
from transformers import (
    AutoTokenizer,
    AutoModelForCausalLM,
    BitsAndBytesConfig
)
from peft import LoraConfig, get_peft_model, TaskType, prepare_model_for_kbit_training
from config import Config

# 尝试导入ModelScope
try:
    from modelscope import AutoTokenizer as MSAutoTokenizer, AutoModelForCausalLM as MSAutoModelForCausalLM
    USE_MODELSCOPE = True
    print("✅ 检测到ModelScope，将使用魔塔社区模型")
except ImportError:
    USE_MODELSCOPE = False
    print("⚠️  未安装ModelScope，将使用HuggingFace")

class ModelSetup:
    def __init__(self):
        self.config = Config()
    
    def load_tokenizer(self):
        """加载tokenizer"""
        print(f"加载tokenizer: {self.config.MODEL_NAME}")

        if USE_MODELSCOPE:
            # 使用ModelScope
            tokenizer = MSAutoTokenizer.from_pretrained(
                self.config.MODEL_NAME,
                trust_remote_code=True,
                cache_dir=self.config.CACHE_DIR
            )
        else:
            # 使用HuggingFace
            tokenizer = AutoTokenizer.from_pretrained(
                self.config.MODEL_NAME,
                trust_remote_code=True,
                cache_dir=self.config.CACHE_DIR
            )

        # 设置特殊token
        if tokenizer.pad_token is None:
            tokenizer.pad_token = tokenizer.eos_token

        return tokenizer
    
    def load_model(self, use_quantization=False):
        """加载模型"""
        print(f"加载模型: {self.config.MODEL_NAME}")

        # 量化配置（如果显存不够可以启用）
        quantization_config = None
        if use_quantization:
            quantization_config = BitsAndBytesConfig(
                load_in_4bit=True,
                bnb_4bit_compute_dtype=torch.float16,
                bnb_4bit_use_double_quant=True,
                bnb_4bit_quant_type="nf4"
            )

        if USE_MODELSCOPE:
            # 使用ModelScope
            model = MSAutoModelForCausalLM.from_pretrained(
                self.config.MODEL_NAME,
                torch_dtype=torch.float16,
                device_map="auto",
                trust_remote_code=True,
                cache_dir=self.config.CACHE_DIR,
                quantization_config=quantization_config
            )
        else:
            # 使用HuggingFace
            model = AutoModelForCausalLM.from_pretrained(
                self.config.MODEL_NAME,
                torch_dtype=torch.float16,
                device_map="auto",
                trust_remote_code=True,
                cache_dir=self.config.CACHE_DIR,
                quantization_config=quantization_config
            )

        # 如果使用量化，需要准备模型
        if use_quantization:
            model = prepare_model_for_kbit_training(model)

        return model
    
    def setup_lora(self, model):
        """设置LoRA配置"""
        print("配置LoRA...")
        
        lora_config = LoraConfig(
            task_type=TaskType.CAUSAL_LM,
            r=self.config.LORA_R,
            lora_alpha=self.config.LORA_ALPHA,
            lora_dropout=self.config.LORA_DROPOUT,
            target_modules=self.config.TARGET_MODULES,
            bias="none",
        )
        
        model = get_peft_model(model, lora_config)
        
        # 打印可训练参数
        model.print_trainable_parameters()
        
        return model
    
    def setup_model_and_tokenizer(self, use_quantization=False):
        """一次性设置模型和tokenizer"""
        tokenizer = self.load_tokenizer()
        model = self.load_model(use_quantization)
        model = self.setup_lora(model)
        
        # 启用梯度检查点以节省显存
        if self.config.GRADIENT_CHECKPOINTING:
            model.enable_input_require_grads()
            model.config.use_cache = False
        
        return model, tokenizer
