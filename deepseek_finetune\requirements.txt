# PyTorch - 请根据您的CUDA版本选择合适的安装命令
# CUDA 11.8: pip install torch==2.1.2 torchvision==0.16.2 torchaudio==2.1.2 --index-url https://download.pytorch.org/whl/cu118
# CUDA 12.1: pip install torch==2.1.2 torchvision==0.16.2 torchaudio==2.1.2 --index-url https://download.pytorch.org/whl/cu121
torch>=2.0.0
torchvision
torchaudio

# 核心依赖
transformers>=4.36.0
peft>=0.7.0
datasets>=2.14.0
accelerate>=0.24.0
bitsandbytes>=0.41.0

# 可选依赖
deepspeed>=0.12.0
wandb
tqdm
numpy
pandas
scikit-learn
tensorboard
psutil
