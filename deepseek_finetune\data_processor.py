"""
数据处理模块 - 处理JSONL数据并转换为训练格式
"""

import json
import random
from typing import List, Dict, Tuple
from datasets import Dataset
from transformers import AutoTokenizer
from config import Config

class DataProcessor:
    def __init__(self, tokenizer: AutoTokenizer):
        self.tokenizer = tokenizer
        self.config = Config()
        
        # 设置pad_token
        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token
    
    def load_jsonl_data(self, file_path: str) -> List[Dict]:
        """加载JSONL文件"""
        data = []
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if line:
                        try:
                            item = json.loads(line)
                            # 验证数据格式
                            if self.validate_data_item(item):
                                data.append(item)
                            else:
                                print(f"警告: 第{line_num}行数据格式不正确，已跳过")
                        except json.JSONDecodeError as e:
                            print(f"警告: 第{line_num}行JSON解析失败: {e}")
            print(f"成功加载 {len(data)} 条数据")
            return data
        except Exception as e:
            print(f"加载数据失败: {e}")
            return []

    def validate_data_item(self, item: Dict) -> bool:
        """验证单条数据格式"""
        required_fields = ['instruction', 'input', 'output']
        for field in required_fields:
            if field not in item:
                return False
            if not isinstance(item[field], str):
                return False
        return True
    
    def format_conversation(self, instruction: str, input_text: str, output_text: str) -> str:
        """格式化对话为DeepSeek格式"""
        # DeepSeek使用特定的对话格式
        if input_text and input_text.strip():
            user_message = f"{instruction}\n\n{input_text}"
        else:
            user_message = instruction

        # 添加特殊token以便更好地训练
        conversation = f"<|user|>\n{user_message}\n<|assistant|>\n{output_text}<|end|>"
        return conversation
    
    def tokenize_function(self, examples):
        """tokenize函数"""
        conversations = []
        for i in range(len(examples['instruction'])):
            conv = self.format_conversation(
                examples['instruction'][i],
                examples['input'][i] if examples['input'][i] else "",
                examples['output'][i]
            )
            conversations.append(conv)

        # tokenize
        tokenized = self.tokenizer(
            conversations,
            truncation=True,
            padding=False,
            max_length=self.config.MAX_LENGTH,
            return_tensors=None,
        )

        # 设置labels（用于计算loss）
        # 需要深拷贝input_ids作为labels
        import copy
        tokenized["labels"] = copy.deepcopy(tokenized["input_ids"])

        return tokenized
    
    def split_data(self, data: List[Dict]) -> Tuple[List[Dict], List[Dict]]:
        """分割训练集和验证集"""
        random.seed(self.config.SEED)
        random.shuffle(data)
        
        split_idx = int(len(data) * (1 - self.config.VALIDATION_SPLIT))
        train_data = data[:split_idx]
        val_data = data[split_idx:]
        
        print(f"训练集: {len(train_data)} 条")
        print(f"验证集: {len(val_data)} 条")
        
        return train_data, val_data
    
    def prepare_datasets(self, data_path: str) -> Tuple[Dataset, Dataset]:
        """准备训练和验证数据集"""
        # 加载数据
        raw_data = self.load_jsonl_data(data_path)
        if not raw_data:
            raise ValueError("无法加载数据")
        
        # 分割数据
        train_data, val_data = self.split_data(raw_data)
        
        # 转换为Dataset格式
        train_dataset = Dataset.from_list(train_data)
        val_dataset = Dataset.from_list(val_data)
        
        # tokenize
        train_dataset = train_dataset.map(
            self.tokenize_function,
            batched=True,
            remove_columns=train_dataset.column_names,
            desc="Tokenizing training data"
        )
        
        val_dataset = val_dataset.map(
            self.tokenize_function,
            batched=True,
            remove_columns=val_dataset.column_names,
            desc="Tokenizing validation data"
        )
        
        return train_dataset, val_dataset
    
    def print_sample(self, dataset: Dataset, num_samples: int = 2):
        """打印数据样本用于检查"""
        print("\n=== 数据样本 ===")
        for i in range(min(num_samples, len(dataset))):
            sample = dataset[i]
            decoded = self.tokenizer.decode(sample['input_ids'], skip_special_tokens=True)
            print(f"\n样本 {i+1}:")
            print(f"长度: {len(sample['input_ids'])}")
            print(f"内容: {decoded[:200]}...")
            print("-" * 50)
