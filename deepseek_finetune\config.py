"""
配置文件 - 所有训练参数都在这里设置
"""

import os

class Config:
    # 模型配置
    MODEL_NAME = "deepseek-ai/deepseek-coder-7b-instruct-v1.5"
    # 如果想用通用对话模型，可以改为：
    # MODEL_NAME = "deepseek-ai/deepseek-llm-7b-chat"
    
    # 数据配置
    DATA_PATH = "./finetune_data/train_alpaca.jsonl"  # 当前目录下的finetune_data文件夹
    OUTPUT_DIR = "./output"
    CACHE_DIR = "./cache"
    
    # LoRA配置
    LORA_R = 16
    LORA_ALPHA = 32
    LORA_DROPOUT = 0.1
    TARGET_MODULES = [
        "q_proj", "k_proj", "v_proj", "o_proj", 
        "gate_proj", "up_proj", "down_proj"
    ]
    
    # 训练配置 - 针对RTX 4090 24GB优化
    LEARNING_RATE = 2e-4
    BATCH_SIZE = 8  # 增大batch size，充分利用24GB显存
    GRADIENT_ACCUMULATION_STEPS = 2  # 减少梯度累积步数
    NUM_EPOCHS = 3
    MAX_LENGTH = 2048
    WARMUP_STEPS = 100
    SAVE_STEPS = 500
    EVAL_STEPS = 500
    LOGGING_STEPS = 10

    # 训练优化 - 针对RTX 4090优化
    FP16 = True
    GRADIENT_CHECKPOINTING = False  # 24GB显存充足，可以关闭以提升速度
    DATALOADER_NUM_WORKERS = 8  # 增加worker数量，利用16核CPU
    
    # 验证配置
    VALIDATION_SPLIT = 0.1  # 10%用作验证集
    
    # 其他配置
    SEED = 42
    RESUME_FROM_CHECKPOINT = None  # 如果要从检查点恢复，设置路径
    
    # wandb配置（可选）
    USE_WANDB = False  # 设置为True启用wandb日志
    WANDB_PROJECT = "deepseek-water-engineering"
    WANDB_RUN_NAME = "deepseek-7b-lora"
    
    @classmethod
    def create_dirs(cls):
        """创建必要的目录"""
        os.makedirs(cls.OUTPUT_DIR, exist_ok=True)
        os.makedirs(cls.CACHE_DIR, exist_ok=True)
